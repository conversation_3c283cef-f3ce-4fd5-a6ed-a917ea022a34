package io.gigsta.presentation.email.components

import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.toComposeImageBitmap
import org.jetbrains.skia.Image

/**
 * iOS implementation for creating ImageBitmap from ByteArray
 */
actual fun createImageBitmapFromByteArray(byteArray: ByteArray): ImageBitmap? {
    return try {
        Image.makeFromEncoded(byteArray).toComposeImageBitmap()
    } catch (e: Exception) {
        null
    }
}
