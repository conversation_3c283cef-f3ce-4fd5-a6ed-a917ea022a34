package io.gigsta.presentation.email.components

import android.graphics.BitmapFactory
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asImageBitmap

/**
 * Android implementation for creating ImageBitmap from ByteArray
 */
actual fun createImageBitmapFromByteArray(byteArray: ByteArray): ImageBitmap? {
    return try {
        val bitmap = BitmapFactory.decodeByteArray(byteArray, 0, byteArray.size)
        bitmap?.asImageBitmap()
    } catch (e: Exception) {
        null
    }
}
