package io.gigsta.presentation.email

import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.ErrorOutline
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.WavingHand
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import io.gigsta.presentation.theme.Spacing

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmailResultScreen(
    onNavigateBack: () -> Unit,
    onNavigateToEdit: () -> Unit,
    viewModel: EmailApplicationViewModel = viewModel(),
    modifier: Modifier = Modifier
) {
    val uiState = viewModel.uiState

    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = {
                    Text(
                        text = "Hasil Email Lamaran",
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Kembali"
                        )
                    }
                },
                actions = {
                    if (uiState.emailApplication != null && !uiState.isGenerating) {
                        IconButton(onClick = onNavigateToEdit) {
                            Icon(
                                imageVector = Icons.Default.Edit,
                                contentDescription = "Edit Input"
                            )
                        }
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surfaceColorAtElevation(2.dp)
                )
            )
        }
    ) { paddingValues ->
        Box(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentAlignment = Alignment.Center
        ) {
            when {
                uiState.isGenerating -> {
                    GeneratingEmailContent(
                        modifier = Modifier.fillMaxSize()
                    )
                }
                uiState.emailApplication != null -> {
                    EmailResultSuccessContent(
                        emailSubject = uiState.emailApplication!!.subject,
                        emailBody = uiState.emailApplication!!.body,
                        onRegenerate = viewModel::regenerateEmail,
                        isRegenerating = uiState.isGenerating,
                        modifier = Modifier.fillMaxSize()
                    )
                }
                uiState.error != null -> {
                    ErrorContent(
                        error = uiState.error!!,
                        onRetry = viewModel::regenerateEmail,
                        onGoBack = onNavigateBack,
                        modifier = Modifier.fillMaxSize()
                    )
                }
                else -> {
                    // Unexpected state, navigate back.
                    LaunchedEffect(Unit) {
                        onNavigateBack()
                    }
                }
            }
        }
    }
}

@Composable
private fun EmailResultSuccessContent(
    emailSubject: String,
    emailBody: String,
    onRegenerate: () -> Unit,
    isRegenerating: Boolean,
    modifier: Modifier = Modifier
) {
    val scrollState = rememberScrollState()
    // val clipboardManager = LocalClipboardManager.current

    Column(
        modifier = modifier
            .verticalScroll(scrollState)
            .padding(Spacing.medium),
        verticalArrangement = Arrangement.spacedBy(Spacing.large)
    ) {
        Text(
            "Email Anda Telah Siap!",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = Spacing.small)
        )

        // Subject Card
        OutlinedCard(modifier = Modifier.fillMaxWidth()) {
            Column(modifier = Modifier.padding(Spacing.medium)) {
                Text("Subjek Email", style = MaterialTheme.typography.titleMedium, fontWeight = FontWeight.SemiBold)
                Spacer(Modifier.height(Spacing.small))
                Text(emailSubject, style = MaterialTheme.typography.bodyLarge)
                Spacer(Modifier.height(Spacing.medium))
                Button(
                    onClick = { /* clipboardManager.setText(AnnotatedString(emailSubject)) */ },
                    modifier = Modifier.align(Alignment.End)
                ) {
                    Text("Salin Subjek")
                }
            }
        }

        // Body Card
        OutlinedCard(modifier = Modifier.fillMaxWidth()) {
            Column(modifier = Modifier.padding(Spacing.medium)) {
                Text("Isi Email", style = MaterialTheme.typography.titleMedium, fontWeight = FontWeight.SemiBold)
                Spacer(Modifier.height(Spacing.small))
                Text(emailBody, style = MaterialTheme.typography.bodyLarge, lineHeight = 24.sp)
                Spacer(Modifier.height(Spacing.medium))
                Button(
                    onClick = { /* clipboardManager.setText(AnnotatedString(emailBody)) */ },
                    modifier = Modifier.align(Alignment.End)
                ) {
                    Text("Salin Isi Email")
                }
            }
        }

        // Actions
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            OutlinedButton(
                onClick = onRegenerate,
                enabled = !isRegenerating,
                modifier = Modifier.weight(1f)
            ) {
                if (isRegenerating) {
                    CircularProgressIndicator(Modifier.size(20.dp))
                } else {
                    Icon(Icons.Default.Refresh, contentDescription = "Buat Ulang")
                    Spacer(Modifier.width(Spacing.small))
                    Text("Buat Ulang")
                }
            }
            Button(
                onClick = { /* TODO: Implement sending email via intent */ },
                modifier = Modifier.weight(1f)
            ) {
                Text("Kirim Email")
            }
        }

        Spacer(Modifier.height(Spacing.medium))
    }
}


@Composable
private fun GeneratingEmailContent(
    modifier: Modifier = Modifier
) {
    val infiniteTransition = rememberInfiniteTransition(label = "generating_animation")
    val rotation by infiniteTransition.animateFloat(
        initialValue = -15f,
        targetValue = 15f,
        animationSpec = infiniteRepeatable(tween(1000)),
        label = "rotation"
    )

    Column(
        modifier = modifier.padding(Spacing.large),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.WavingHand,
            contentDescription = null,
            modifier = Modifier
                .size(64.dp)
                .rotate(rotation),
            tint = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.height(Spacing.extraLarge))

        Text(
            text = "Sedang Meracik Email Terbaik...",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.SemiBold,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(Spacing.medium))

        Text(
            text = "AI kami sedang menganalisis resume dan lowongan untuk membuat email yang paling menonjol.",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(Spacing.large))
        LinearProgressIndicator(modifier = Modifier.fillMaxWidth())
    }
}

@Composable
private fun ErrorContent(
    error: String,
    onRetry: () -> Unit,
    onGoBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(Spacing.large),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.ErrorOutline,
            contentDescription = null,
            modifier = Modifier.size(80.dp),
            tint = MaterialTheme.colorScheme.error
        )

        Spacer(modifier = Modifier.height(Spacing.large))

        Text(
            text = "Gagal Membuat Email",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(Spacing.medium))

        Text(
            text = "Terjadi kesalahan saat kami mencoba membuat email untuk Anda. Silakan coba lagi.",
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(Spacing.medium))

        Text(
            text = "Detail: $error",
            style = MaterialTheme.typography.bodySmall,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onErrorContainer,
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    MaterialTheme.colorScheme.errorContainer,
                    shape = MaterialTheme.shapes.medium
                )
                .padding(Spacing.medium)
        )

        Spacer(modifier = Modifier.height(Spacing.extraLarge))

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            OutlinedButton(
                onClick = onGoBack,
                modifier = Modifier.weight(1f)
            ) {
                Text("Kembali")
            }

            Button(
                onClick = onRetry,
                modifier = Modifier.weight(1f)
            ) {
                Icon(Icons.Default.Refresh, contentDescription = null)
                Spacer(Modifier.width(Spacing.small))
                Text("Coba Lagi")
            }
        }
    }
}
