package io.gigsta.presentation.email

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import io.gigsta.domain.model.InputMethod
import io.gigsta.presentation.email.components.JobInfoInputSection
import io.gigsta.presentation.email.components.ResumeUploadSection
import io.gigsta.presentation.theme.Spacing
import io.gigsta.utils.rememberUrlOpener

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmailApplicationScreen(
    onNavigateBack: () -> Unit,
    onNavigateToResult: () -> Unit,
    viewModel: EmailApplicationViewModel = viewModel(),
    modifier: Modifier = Modifier
) {
    val uiState = viewModel.uiState
    val scrollState = rememberScrollState()
    val urlOpener = rememberUrlOpener()

    // Navigate to result screen when email generation starts
    LaunchedEffect(uiState.isGenerating) {
        if (uiState.isGenerating) {
            onNavigateToResult()
        }
    }

    // Handle opening resume URL
    LaunchedEffect(uiState.shouldOpenResumeUrl, uiState.resumeUrl) {
        if (uiState.shouldOpenResumeUrl && uiState.resumeUrl != null) {
            urlOpener.openUrl(uiState.resumeUrl)
            viewModel.onResumeUrlOpened()
        }
    }

    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = {
                    Text(
                        text = "Email Lamaran AI",
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Kembali"
                        )
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surfaceColorAtElevation(2.dp)
                )
            )
        },
        bottomBar = {
            val canGenerate = uiState.isResumeUploaded && when (uiState.inputMethod) {
                InputMethod.TEXT -> uiState.jobDescription.isNotBlank()
                InputMethod.IMAGE -> uiState.hasJobImage
            }

            Surface(
                modifier = Modifier.fillMaxWidth(),
                shadowElevation = 8.dp
            ) {
                Button(
                    onClick = viewModel::generateEmail,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(Spacing.medium),
                    enabled = canGenerate && !uiState.isGenerating,
                    contentPadding = PaddingValues(Spacing.medium)
                ) {
                    if (uiState.isGenerating) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            strokeWidth = 2.dp,
                            color = MaterialTheme.colorScheme.onPrimary
                        )
                        Spacer(modifier = Modifier.width(Spacing.medium))
                        Text("Memproses...")
                    } else {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.Send,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(Spacing.small))
                        Text("Buat Email Lamaran")
                    }
                }
            }
        }
    ) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(scrollState)
                .padding(Spacing.medium),
            verticalArrangement = Arrangement.spacedBy(Spacing.large)
        ) {
            // Overall error message if not specific to a section
            AnimatedVisibility(uiState.error != null && !uiState.error!!.contains("resume", true) && !uiState.error!!.contains("lowongan", true)) {
                uiState.error?.let { error ->
                    ErrorCard(error)
                }
            }

            // Step 1: Resume
            SectionCard(
                title = "Langkah 1: Unggah Resume",
                subtitle = "Pastikan resume Anda terbaru dan dalam format PDF."
            ) {
                ResumeUploadSection(
                    isLoading = uiState.isResumeLoading || uiState.isResumeUploading,
                    uploadSuccess = uiState.isResumeUploaded,
                    existingResume = uiState.resumeInfo,
                    error = if (uiState.error?.contains("resume", ignoreCase = true) == true) uiState.error else null,
                    onFileSelected = { fileData, fileName, mimeType ->
                        viewModel.onResumeUploaded(fileData, fileName, mimeType)
                    },
                    onViewResume = viewModel::onViewResume,
                    onDeleteResume = viewModel::onDeleteResume,
                    isDeleting = uiState.isResumeDeleting,
                    isGettingUrl = uiState.isGettingResumeUrl
                )
            }

            // Step 2: Job Info
            SectionCard(
                title = "Langkah 2: Informasi Lowongan",
                subtitle = "Salin-tempel deskripsi atau unggah screenshot lowongan."
            ) {
                JobInfoInputSection(
                    inputMethod = uiState.inputMethod,
                    jobDescription = uiState.jobDescription,
                    onJobDescriptionChange = viewModel::onJobDescriptionChanged,
                    onInputMethodChange = viewModel::onInputMethodChanged,
                    onImageSelected = { imageData, fileName, mimeType ->
                        viewModel.onJobImageSelected(imageData, fileName, mimeType)
                    },
                    hasJobImage = uiState.hasJobImage,
                    error = if (uiState.error?.contains("lowongan") == true ||
                        uiState.error?.contains("gambar") == true ||
                        uiState.error?.contains("deskripsi") == true) uiState.error else null
                )
            }

            // Spacer for bottom bar
            Spacer(modifier = Modifier.height(Spacing.large))
        }
    }
}

@Composable
private fun SectionCard(
    title: String,
    subtitle: String,
    content: @Composable ColumnScope.() -> Unit
) {
    OutlinedCard(
        modifier = Modifier.fillMaxWidth(),
        shape = MaterialTheme.shapes.large
    ) {
        Column(
            modifier = Modifier.padding(Spacing.medium)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.SemiBold
            )
            Spacer(modifier = Modifier.height(Spacing.extraSmall))
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.height(Spacing.medium))

            content()
        }
    }
}

@Composable
private fun ErrorCard(error: String) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        ),
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.padding(Spacing.medium),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Warning,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onErrorContainer
            )
            Spacer(modifier = Modifier.width(Spacing.small))
            Text(
                text = error,
                color = MaterialTheme.colorScheme.onErrorContainer,
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}


